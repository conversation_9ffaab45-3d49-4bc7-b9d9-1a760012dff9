import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CompanySelectorComponent } from './components/company-selector/company-selector.component';
import { MemberComponent } from './components/member/member.component';
import { CompanyuserComponent } from './components/companyuser/companyuser.component';
import { CompanyUserDetailsComponent } from './components/company-user-details/company-user-details.component';
import { MemberRemainingDayComponent } from './components/member-remaining-day/member-remaining-day.component';
import { MemberentryexithistoryComponent } from './components/memberentryexithistory/memberentryexithistory.component';
import { MemberFilterComponent } from './components/member-filter/member-filter.component';
import { PaymentHistoryComponent } from './components/payment-history/payment-history.component';
import { CompanyUnifiedAddComponent } from './components/crud/company-unified-add/company-unified-add.component';
import { MemberAddComponent } from './components/crud/member-add/member-add.component';
import { MembershiptypeAddComponent } from './components/crud/membershiptype-add/membershiptype-add.component';
import { MembershipAddComponent } from './components/crud/membership-add/membership-add.component';
import { LoginComponent } from './components/login/login.component';
import { ChangePasswordComponent } from './components/change-password/change-password.component';
import { AppUnauthorizedComponent } from './components/app-unauthorized/app-unauthorized.component';
import { LoginGuard } from './guards/login.guard';
import { antiLoginGuard } from './guards/anti-login.guard';
import { roleGuard } from './guards/role-guard.guard';
import { PasswordChangeGuard } from './guards/password-change/password-change.guard';
import { MemberQRCodeComponent } from './components/member-qrcode/member-qrcode.component';
import { DebtorMemberComponent } from './components/debtor-member/debtor-member.component';
import { TodayEntriesComponent } from './components/today-entries/today-entries.component';
import { ProductListComponent } from './components/product-list/product-list.component';
import { TransactionListComponent } from './components/transaction-list/transaction-list.component';
import { MemberBalanceTopupComponent } from './components/member-balance-topup/member-balance-topup.component';
import { ProductSaleComponent } from './components/product-sale/product-sale.component';
import { OperationClaimComponent } from './components/operation-claim/operation-claim.component';
import { UserOperationClaimComponent } from './components/user-operation-claim/user-operation-claim.component';
import { DevicesComponent } from './components/devices/devices.component';
import { FrozenMembershipsComponent } from './components/frozen-memberships/frozen-memberships.component';
import { LicenseDashboardComponent } from './components/license-dashboard/license-dashboard.component';
import { LicensePackagesListComponent } from './components/license-packages-list/license-packages-list.component';
import { LicenseTransactionsComponent } from './components/license-transactions/license-transactions.component';
import { UserLicensesListComponent } from './components/user-licenses-list/user-licenses-list.component';
import { LicenseExpiredComponent } from './components/license-expired/license-expired.component';
import { RegisterComponent } from './components/register/register.component';
import { BirthdayPanelComponent } from './components/birthday-panel/birthday-panel.component';
import { ExpenseManagementComponent } from './components/expense-management/expense-management.component'; // Eklendi
import { ProfileComponent } from './components/profile/profile.component';
import { MyQRComponent } from './components/my-qr/my-qr.component';
import { RateLimitTestComponent } from './components/rate-limit-test/rate-limit-test.component';
import { CacheAdminComponent } from './components/cache-admin/cache-admin.component';
import { ExerciseListComponent } from './components/exercise-list/exercise-list.component';
import { WorkoutProgramListComponent } from './components/workout-programs/workout-program-list.component';
import { WorkoutProgramEditComponent } from './components/workout-programs/workout-program-edit.component';
import { WorkoutProgramDetailComponent } from './components/workout-programs/workout-program-detail.component';
import { WorkoutProgramWizardComponent } from './components/workout-programs/workout-program-wizard.component';
import { MemberWorkoutAssignmentsComponent } from './components/member-workout-assignments/member-workout-assignments.component';

const routes: Routes = [
  { path: 'company-selector', component: CompanySelectorComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'license-expired', component: LicenseExpiredComponent },
  { path: "", component: LoginComponent, canActivate: [antiLoginGuard]},
  { path: 'license-dashboard', component: LicenseDashboardComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'license-packages', component: LicensePackagesListComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'user-licenses', component: UserLicensesListComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'license-transactions', component: LicenseTransactionsComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'devices',component: DevicesComponent,canActivate: [LoginGuard, roleGuard],data: { expectedRole: ['owner'] }},
  { path: "companyuserdetails", component: CompanyUserDetailsComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' }},
  { path: "companyuser", component: CompanyuserComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' }},
  { path: "companyuser/getcompanyuserdetailsbycityid/:cityID", component: CompanyUserDetailsComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' }},
  { path: "allmembers", component: MemberComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "frozen-memberships", component: FrozenMembershipsComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "memberremainingday", component: MemberRemainingDayComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "memberfilter", component: MemberFilterComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "paymenthistory", component: PaymentHistoryComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "company/unified-add", component: CompanyUnifiedAddComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' }},
  { path: "member/add", component: MemberAddComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "membershiptype/add", component: MembershiptypeAddComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "membership/add", component: MembershipAddComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: "login", component: LoginComponent, canActivate: [antiLoginGuard]},
  { path: "unauthorized", component: AppUnauthorizedComponent },
  { path: "debtormember", component: DebtorMemberComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'todayentries', component: TodayEntriesComponent, canActivate: [LoginGuard, PasswordChangeGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'products', component: ProductListComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] } },
  { path: 'transactions', component: TransactionListComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] } },
  { path: 'memberbalancetopup', component: MemberBalanceTopupComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] } },
  { path: 'product-sale', component: ProductSaleComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] } },
  { path: 'qr', component: MemberQRCodeComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] } },
  { path: 'register', component: RegisterComponent },
  { path: 'change-password', component: ChangePasswordComponent, canActivate: [LoginGuard] },
  { path: 'roles', component: OperationClaimComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'user-roles', component: UserOperationClaimComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' } },
  { path: 'birthdays', component: BirthdayPanelComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'expenses', component: ExpenseManagementComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }}, // Gider Yönetimi route'u eklendi
  { path: 'profile', component: ProfileComponent, canActivate: [LoginGuard, PasswordChangeGuard] },
  { path: 'my-qr', component: MyQRComponent, canActivate: [LoginGuard, PasswordChangeGuard, roleGuard], data: { expectedRole: 'member' }},
  { path: 'rate-limit-test', component: RateLimitTestComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' }},
  { path: 'cache-admin', component: CacheAdminComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: 'owner' }},
  { path: 'exercises', component: ExerciseListComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'workout-programs', component: WorkoutProgramListComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'workout-programs/add', component: WorkoutProgramWizardComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'workout-programs/edit/:id', component: WorkoutProgramEditComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'workout-programs/detail/:id', component: WorkoutProgramDetailComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }},
  { path: 'member-workout-assignments', component: MemberWorkoutAssignmentsComponent, canActivate: [LoginGuard, roleGuard], data: { expectedRole: ['owner','admin'] }}
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
