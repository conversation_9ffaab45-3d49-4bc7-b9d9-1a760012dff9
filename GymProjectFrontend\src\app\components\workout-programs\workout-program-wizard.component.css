/* Workout Program Wizard Styles */

/* Wizard Progress */
.wizard-progress {
  padding: 1rem 0;
}

.steps-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  cursor: default;
  transition: all 0.3s ease;
}

.step-item.clickable {
  cursor: pointer;
}

.step-item.clickable:hover .step-circle {
  transform: scale(1.1);
}

.step-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.step-item.active .step-circle {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
  box-shadow: 0 0 0 4px var(--primary-light);
}

.step-item.completed .step-circle {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

.step-icon {
  font-size: 1.2rem;
}

.step-content {
  text-align: center;
  max-width: 120px;
}

.step-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.step-item.active .step-title {
  color: var(--primary);
}

.step-item.completed .step-title {
  color: var(--success);
}

.step-description {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.2;
}

.step-connector {
  position: absolute;
  top: 25px;
  left: 50%;
  right: -50%;
  height: 2px;
  background-color: var(--border-color);
  z-index: 1;
  transition: background-color 0.3s ease;
}

.step-connector.completed {
  background-color: var(--success);
}

.step-item:last-child .step-connector {
  display: none;
}

/* Wizard Content */
.wizard-content {
  min-height: 400px;
  margin-bottom: 2rem;
}

.step-content-wrapper {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Day Count Selection */
.day-count-selection {
  text-align: center;
}

.day-count-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.day-count-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.day-count-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.day-count-card.selected {
  border-color: var(--primary);
  background: var(--primary-light);
  color: var(--primary);
}

.day-count-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary);
}

.day-count-number {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.day-count-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.day-count-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Days Setup */
.days-setup-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.day-setup-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.day-setup-card:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-light);
}

.day-setup-card.cdk-drag-preview {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary);
  background: var(--bg-primary);
  transform: rotate(2deg);
}

.day-setup-card.cdk-drag-placeholder {
  opacity: 0.3;
  border-style: dashed;
}

.day-drag-handle {
  color: var(--text-secondary);
  cursor: grab;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
}

.day-drag-handle:hover {
  background: var(--bg-tertiary);
  color: var(--primary);
}

.day-drag-handle:active {
  cursor: grabbing;
}

.day-setup-content {
  flex: 1;
}

.day-number {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

/* Wizard Navigation */
.wizard-navigation {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 1rem;
  margin-top: 2rem;
  border-radius: var(--border-radius-lg);
}

.step-indicator {
  background: var(--bg-tertiary);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-pill);
  font-weight: 600;
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .steps-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step-connector {
    display: none;
  }
  
  .day-count-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .day-setup-card {
    flex-direction: column;
    align-items: stretch;
  }
  
  .day-drag-handle {
    align-self: center;
    order: -1;
  }
}

@media (max-width: 576px) {
  .day-count-grid {
    grid-template-columns: 1fr;
  }
  
  .wizard-navigation .d-flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step-indicator {
    text-align: center;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .day-count-card {
  background: var(--bg-secondary);
}

[data-theme="dark"] .day-count-card.selected {
  background: var(--primary-light);
}

[data-theme="dark"] .day-setup-card {
  background: var(--bg-secondary);
}

[data-theme="dark"] .day-setup-card.cdk-drag-preview {
  background: var(--bg-secondary);
}

/* Exercise Setup Styles */
.exercise-setup-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.exercise-day-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all 0.3s ease;
}

.exercise-day-card:hover {
  box-shadow: var(--shadow-sm);
}

.exercise-day-card.rest-day {
  opacity: 0.7;
  background: var(--bg-secondary);
}

.exercise-day-header {
  background: var(--bg-tertiary);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.day-badge {
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 0.5rem;
}

.rest-day-badge {
  background: var(--warning-light);
  color: var(--warning);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.8rem;
  font-weight: 600;
}

.exercise-count-badge {
  background: var(--success-light);
  color: var(--success);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.8rem;
  font-weight: 600;
}

.exercise-day-content {
  padding: 1rem;
}

.exercise-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.exercise-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.exercise-order {
  width: 30px;
  height: 30px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.exercise-info {
  flex: 1;
}

.exercise-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.exercise-details {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.exercise-notes {
  font-style: italic;
  color: var(--info);
}

.exercise-actions {
  display: flex;
  gap: 0.5rem;
}

.add-exercise-section {
  text-align: center;
  padding: 1rem;
  border-top: 1px dashed var(--border-color);
}

/* Preview Styles */
.program-preview {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.preview-section {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
}

.preview-section-title {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-light);
}

.preview-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.preview-info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-info-item.full-width {
  grid-column: 1 / -1;
}

.preview-info-item label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.preview-info-item span {
  color: var(--text-primary);
  font-weight: 500;
}

.preview-days-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.preview-day-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.preview-day-card.rest-day {
  opacity: 0.7;
  border-color: var(--warning);
}

.preview-day-header {
  background: var(--bg-tertiary);
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.preview-day-number {
  background: var(--primary);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.preview-day-name {
  font-weight: 600;
  color: var(--text-primary);
}

.preview-day-content {
  padding: 0.75rem;
}

.preview-rest-day {
  text-align: center;
  color: var(--warning);
  font-style: italic;
  padding: 1rem;
}

.preview-exercise-count {
  font-weight: 600;
  color: var(--success);
  margin-bottom: 0.5rem;
}

.preview-exercise-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-exercise-item {
  font-size: 0.9rem;
  color: var(--text-secondary);
  padding: 0.25rem 0;
}

.preview-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.preview-stat-item {
  text-align: center;
  background: var(--bg-primary);
  padding: 1rem;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.preview-stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.preview-stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}
