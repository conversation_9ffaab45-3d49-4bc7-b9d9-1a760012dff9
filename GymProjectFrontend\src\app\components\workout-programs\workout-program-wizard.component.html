<div class="container-fluid">
  <!-- Header -->
  <div class="modern-card mb-4">
    <div class="modern-card-header">
      <div class="d-flex align-items-center">
        <button 
          class="modern-btn modern-btn-outline-secondary me-3"
          (click)="goBack()">
          <fa-icon [icon]="faArrowLeft"></fa-icon>
        </button>
        <h4 class="mb-0"><PERSON>ni <PERSON>man Programı Oluştur</h4>
      </div>
      <div class="d-flex gap-2">
        <button 
          type="button"
          class="modern-btn modern-btn-secondary"
          (click)="goBack()">
          İptal
        </button>
        <button 
          *ngIf="currentStep === totalSteps"
          type="button"
          class="modern-btn modern-btn-primary"
          [disabled]="isSubmitting || !validateAllSteps()"
          (click)="onSubmit()">
          <fa-icon [icon]="faSave" class="modern-btn-icon"></fa-icon>
          <span *ngIf="!isSubmitting">Kaydet</span>
          <span *ngIf="isSubmitting">Kaydediliyor...</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Progress Steps -->
  <div class="modern-card mb-4">
    <div class="modern-card-body">
      <div class="wizard-progress">
        <div class="steps-container">
          <div 
            *ngFor="let step of steps; let i = index"
            class="step-item"
            [class.active]="step.isActive"
            [class.completed]="step.isCompleted"
            [class.clickable]="i < currentStep || step.isCompleted"
            (click)="goToStep(step.id)">
            
            <div class="step-circle">
              <fa-icon 
                *ngIf="step.isCompleted" 
                [icon]="faCheck"
                class="step-icon completed"></fa-icon>
              <fa-icon 
                *ngIf="!step.isCompleted" 
                [icon]="step.icon"
                class="step-icon"></fa-icon>
            </div>
            
            <div class="step-content">
              <div class="step-title">{{step.title}}</div>
              <div class="step-description">{{step.description}}</div>
            </div>
            
            <div 
              *ngIf="i < steps.length - 1" 
              class="step-connector"
              [class.completed]="step.isCompleted"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Step Content -->
  <div class="wizard-content">
    
    <!-- Step 1: Program Info -->
    <div *ngIf="currentStep === 1" class="step-content-wrapper">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
            Program Bilgileri
          </h5>
        </div>
        <div class="modern-card-body">
          <form [formGroup]="programInfoForm">
            <div class="row g-3">
              <!-- Program Name -->
              <div class="col-md-6">
                <div class="modern-form-group">
                  <label class="modern-form-label">
                    Program Adı <span class="text-danger">*</span>
                  </label>
                  <input 
                    type="text" 
                    class="modern-form-control"
                    [class.is-invalid]="isFieldInvalid(programInfoForm, 'programName')"
                    formControlName="programName"
                    placeholder="Örn: Başlangıç Kas Yapma Programı">
                  <div *ngIf="isFieldInvalid(programInfoForm, 'programName')" class="invalid-feedback">
                    {{getFieldError(programInfoForm, 'programName')}}
                  </div>
                </div>
              </div>

              <!-- Experience Level -->
              <div class="col-md-3">
                <div class="modern-form-group">
                  <label class="modern-form-label">
                    Deneyim Seviyesi <span class="text-danger">*</span>
                  </label>
                  <select 
                    class="modern-form-control"
                    [class.is-invalid]="isFieldInvalid(programInfoForm, 'experienceLevel')"
                    formControlName="experienceLevel">
                    <option value="">Seçiniz</option>
                    <option *ngFor="let level of experienceLevels" [value]="level.value">
                      {{level.label}}
                    </option>
                  </select>
                  <div *ngIf="isFieldInvalid(programInfoForm, 'experienceLevel')" class="invalid-feedback">
                    {{getFieldError(programInfoForm, 'experienceLevel')}}
                  </div>
                </div>
              </div>

              <!-- Target Goal -->
              <div class="col-md-3">
                <div class="modern-form-group">
                  <label class="modern-form-label">
                    Hedef <span class="text-danger">*</span>
                  </label>
                  <select 
                    class="modern-form-control"
                    [class.is-invalid]="isFieldInvalid(programInfoForm, 'targetGoal')"
                    formControlName="targetGoal">
                    <option value="">Seçiniz</option>
                    <option *ngFor="let goal of targetGoals" [value]="goal.value">
                      {{goal.label}}
                    </option>
                  </select>
                  <div *ngIf="isFieldInvalid(programInfoForm, 'targetGoal')" class="invalid-feedback">
                    {{getFieldError(programInfoForm, 'targetGoal')}}
                  </div>
                </div>
              </div>

              <!-- Description -->
              <div class="col-12">
                <div class="modern-form-group">
                  <label class="modern-form-label">Açıklama (Opsiyonel)</label>
                  <textarea 
                    class="modern-form-control"
                    [class.is-invalid]="isFieldInvalid(programInfoForm, 'description')"
                    formControlName="description"
                    rows="3"
                    placeholder="Program hakkında detaylı bilgi..."></textarea>
                  <div *ngIf="isFieldInvalid(programInfoForm, 'description')" class="invalid-feedback">
                    {{getFieldError(programInfoForm, 'description')}}
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Step 2: Day Count Selection -->
    <div *ngIf="currentStep === 2" class="step-content-wrapper">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
            Kaç Günlük Program?
          </h5>
        </div>
        <div class="modern-card-body">
          <form [formGroup]="dayCountForm">
            <div class="day-count-selection">
              <p class="text-muted mb-4">Antrenman programınızın kaç gün olacağını seçin:</p>
              
              <div class="day-count-grid">
                <div 
                  *ngFor="let count of [1,2,3,4,5,6,7]" 
                  class="day-count-card"
                  [class.selected]="dayCountForm.get('dayCount')?.value === count"
                  (click)="dayCountForm.get('dayCount')?.setValue(count)">
                  
                  <div class="day-count-number">{{count}}</div>
                  <div class="day-count-label">
                    {{count === 1 ? 'Gün' : 'Gün'}}
                  </div>
                  
                  <div class="day-count-description">
                    <span *ngIf="count === 1">Tek günlük program</span>
                    <span *ngIf="count === 2">Hafta içi 2 gün</span>
                    <span *ngIf="count === 3">Haftada 3 gün</span>
                    <span *ngIf="count === 4">Haftada 4 gün</span>
                    <span *ngIf="count === 5">Haftada 5 gün</span>
                    <span *ngIf="count === 6">Haftada 6 gün</span>
                    <span *ngIf="count === 7">Her gün antrenman</span>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Step 3: Days Setup -->
    <div *ngIf="currentStep === 3" class="step-content-wrapper">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
            Günleri Ayarlayın ({{selectedDayCount}} Gün)
          </h5>
          <small class="text-muted">Günleri sürükleyerek sıralayabilirsiniz</small>
        </div>
        <div class="modern-card-body">
          <form [formGroup]="daysSetupForm">
            <div
              cdkDropList
              (cdkDropListDropped)="dropDay($event)"
              class="days-setup-container">
              
              <div 
                *ngFor="let dayControl of daysArray.controls; let i = index"
                cdkDrag
                class="day-setup-card"
                [formGroupName]="i">
                
                <div class="day-drag-handle" cdkDragHandle>
                  <i class="fas fa-grip-vertical"></i>
                </div>
                
                <div class="day-setup-content">
                  <div class="day-number">{{i + 1}}. Gün</div>
                  
                  <div class="row g-3">
                    <!-- Day Name -->
                    <div class="col-md-6">
                      <div class="modern-form-group">
                        <label class="modern-form-label">
                          Gün Adı <span class="text-danger">*</span>
                        </label>
                        <input 
                          type="text" 
                          class="modern-form-control"
                          formControlName="dayName"
                          placeholder="Örn: Göğüs-Triceps">
                      </div>
                    </div>

                    <!-- Popular Day Names -->
                    <div class="col-md-4">
                      <div class="modern-form-group">
                        <label class="modern-form-label">Hızlı Seçim</label>
                        <select 
                          class="modern-form-control"
                          (change)="onPopularDayNameSelect(i, $any($event.target).value)">
                          <option value="">Popüler gün adları</option>
                          <option *ngFor="let dayName of popularDayNames" [value]="dayName">
                            {{dayName}}
                          </option>
                        </select>
                      </div>
                    </div>

                    <!-- Rest Day -->
                    <div class="col-md-2">
                      <div class="modern-form-group">
                        <label class="modern-form-label">Dinlenme</label>
                        <div class="form-check form-switch">
                          <input 
                            class="form-check-input" 
                            type="checkbox" 
                            formControlName="isRestDay"
                            (change)="onRestDayChange(i)"
                            [id]="'restDay' + i">
                          <label class="form-check-label" [for]="'restDay' + i">
                            {{dayControl.get('isRestDay')?.value ? 'Evet' : 'Hayır'}}
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Step 4: Exercise Setup -->
    <div *ngIf="currentStep === 4" class="step-content-wrapper">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faDumbbell" class="me-2"></fa-icon>
            Egzersizleri Ekleyin
          </h5>
          <small class="text-muted">Her antrenman günü için egzersizleri belirleyin</small>
        </div>
        <div class="modern-card-body">
          <div class="exercise-setup-container">
            <div
              *ngFor="let day of programDays; let i = index"
              class="exercise-day-card"
              [class.rest-day]="day.isRestDay">

              <div class="exercise-day-header">
                <h6 class="mb-0">
                  <span class="day-badge">{{day.dayNumber}}. Gün</span>
                  {{day.dayName}}
                </h6>
                <div *ngIf="day.isRestDay" class="rest-day-badge">
                  Dinlenme Günü
                </div>
                <div *ngIf="!day.isRestDay" class="exercise-count-badge">
                  {{day.exercises?.length || 0}} Egzersiz
                </div>
              </div>

              <div *ngIf="!day.isRestDay" class="exercise-day-content">
                <div class="exercise-list">
                  <div
                    *ngFor="let exercise of day.exercises; let j = index"
                    class="exercise-item">
                    <div class="exercise-order">{{j + 1}}</div>
                    <div class="exercise-info">
                      <div class="exercise-name">{{exercise.exerciseName || 'Egzersiz Seçin'}}</div>
                      <div class="exercise-details">
                        {{exercise.sets}} set × {{exercise.reps}} tekrar
                        <span *ngIf="exercise.notes" class="exercise-notes">- {{exercise.notes}}</span>
                      </div>
                    </div>
                    <div class="exercise-actions">
                      <button
                        type="button"
                        class="modern-btn modern-btn-outline-primary modern-btn-sm"
                        (click)="editExercise(i, j)">
                        Düzenle
                      </button>
                      <button
                        type="button"
                        class="modern-btn modern-btn-outline-danger modern-btn-sm"
                        (click)="removeExercise(i, j)">
                        Sil
                      </button>
                    </div>
                  </div>
                </div>

                <div class="add-exercise-section">
                  <button
                    type="button"
                    class="modern-btn modern-btn-outline-primary"
                    (click)="addExercise(i)">
                    <fa-icon [icon]="faPlus" class="me-2"></fa-icon>
                    Egzersiz Ekle
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 5: Preview -->
    <div *ngIf="currentStep === 5" class="step-content-wrapper">
      <div class="modern-card">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <fa-icon [icon]="faEye" class="me-2"></fa-icon>
            Program Önizlemesi
          </h5>
          <small class="text-muted">Programınızı kontrol edin ve kaydedin</small>
        </div>
        <div class="modern-card-body">
          <div class="program-preview">
            <!-- Program Info Summary -->
            <div class="preview-section">
              <h6 class="preview-section-title">Program Bilgileri</h6>
              <div class="preview-info-grid">
                <div class="preview-info-item">
                  <label>Program Adı:</label>
                  <span>{{programInfoForm.get('programName')?.value}}</span>
                </div>
                <div class="preview-info-item">
                  <label>Deneyim Seviyesi:</label>
                  <span>{{programInfoForm.get('experienceLevel')?.value}}</span>
                </div>
                <div class="preview-info-item">
                  <label>Hedef:</label>
                  <span>{{programInfoForm.get('targetGoal')?.value}}</span>
                </div>
                <div class="preview-info-item">
                  <label>Gün Sayısı:</label>
                  <span>{{selectedDayCount}} Gün</span>
                </div>
                <div *ngIf="programInfoForm.get('description')?.value" class="preview-info-item full-width">
                  <label>Açıklama:</label>
                  <span>{{programInfoForm.get('description')?.value}}</span>
                </div>
              </div>
            </div>

            <!-- Days Summary -->
            <div class="preview-section">
              <h6 class="preview-section-title">Program Günleri</h6>
              <div class="preview-days-grid">
                <div
                  *ngFor="let day of programDays"
                  class="preview-day-card"
                  [class.rest-day]="day.isRestDay">

                  <div class="preview-day-header">
                    <span class="preview-day-number">{{day.dayNumber}}</span>
                    <span class="preview-day-name">{{day.dayName}}</span>
                  </div>

                  <div class="preview-day-content">
                    <div *ngIf="day.isRestDay" class="preview-rest-day">
                      Dinlenme Günü
                    </div>
                    <div *ngIf="!day.isRestDay" class="preview-exercises">
                      <div class="preview-exercise-count">
                        {{day.exercises?.length || 0}} Egzersiz
                      </div>
                      <div class="preview-exercise-list">
                        <div
                          *ngFor="let exercise of day.exercises; let j = index"
                          class="preview-exercise-item">
                          {{j + 1}}. {{exercise.exerciseName}} ({{exercise.sets}}×{{exercise.reps}})
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Program Stats -->
            <div class="preview-section">
              <h6 class="preview-section-title">Program İstatistikleri</h6>
              <div class="preview-stats-grid">
                <div class="preview-stat-item">
                  <div class="preview-stat-number">{{selectedDayCount}}</div>
                  <div class="preview-stat-label">Toplam Gün</div>
                </div>
                <div class="preview-stat-item">
                  <div class="preview-stat-number">{{getWorkoutDaysCount()}}</div>
                  <div class="preview-stat-label">Antrenman Günü</div>
                </div>
                <div class="preview-stat-item">
                  <div class="preview-stat-number">{{getRestDaysCount()}}</div>
                  <div class="preview-stat-label">Dinlenme Günü</div>
                </div>
                <div class="preview-stat-item">
                  <div class="preview-stat-number">{{getTotalExerciseCount()}}</div>
                  <div class="preview-stat-label">Toplam Egzersiz</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- Navigation -->
  <div class="wizard-navigation">
    <div class="d-flex justify-content-between">
      <button 
        type="button"
        class="modern-btn modern-btn-outline-secondary"
        [disabled]="currentStep === 1"
        (click)="previousStep()">
        <fa-icon [icon]="faArrowLeft" class="me-2"></fa-icon>
        Önceki
      </button>

      <div class="step-indicator">
        {{currentStep}} / {{totalSteps}}
      </div>

      <button 
        type="button"
        class="modern-btn modern-btn-primary"
        [disabled]="!validateCurrentStep() || currentStep === totalSteps"
        (click)="nextStep()">
        İleri
        <fa-icon [icon]="faArrowRight" class="ms-2"></fa-icon>
      </button>
    </div>
  </div>
</div>
