import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import {
  faArrowLeft,
  faArrowRight,
  faCheck,
  faSave,
  faCalendarAlt,
  faInfoCircle,
  faDumbbell,
  faEye,
  faPlus,
  faEdit,
  faTrash
} from '@fortawesome/free-solid-svg-icons';

import { WorkoutProgramService } from '../../services/workout-program.service';
import {
  WorkoutProgramTemplateAdd,
  WorkoutProgramDayAdd,
  WorkoutProgramExerciseAdd,
  POPULAR_DAY_NAMES,
  EXPERIENCE_LEVELS,
  TARGET_GOALS
} from '../../models/workout-program.models';

export interface WizardStep {
  id: number;
  title: string;
  description: string;
  icon: any;
  isCompleted: boolean;
  isActive: boolean;
}

@Component({
  selector: 'app-workout-program-wizard',
  templateUrl: './workout-program-wizard.component.html',
  styleUrls: ['./workout-program-wizard.component.css'],
  standalone: false
})
export class WorkoutProgramWizardComponent implements OnInit {
  // Icons
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faSave = faSave;
  faCalendarAlt = faCalendarAlt;
  faInfoCircle = faInfoCircle;
  faDumbbell = faDumbbell;
  faEye = faEye;
  faPlus = faPlus;
  faEdit = faEdit;
  faTrash = faTrash;

  // Wizard State
  currentStep = 1;
  totalSteps = 5;
  isSubmitting = false;

  // Steps Configuration
  steps: WizardStep[] = [
    {
      id: 1,
      title: 'Program Bilgileri',
      description: 'Temel program bilgilerini girin',
      icon: this.faInfoCircle,
      isCompleted: false,
      isActive: true
    },
    {
      id: 2,
      title: 'Gün Sayısı',
      description: 'Kaç günlük program oluşturacağınızı seçin',
      icon: this.faCalendarAlt,
      isCompleted: false,
      isActive: false
    },
    {
      id: 3,
      title: 'Günleri Ayarla',
      description: 'Her günün adını ve türünü belirleyin',
      icon: this.faCalendarAlt,
      isCompleted: false,
      isActive: false
    },
    {
      id: 4,
      title: 'Egzersizler',
      description: 'Her güne egzersizleri ekleyin',
      icon: this.faDumbbell,
      isCompleted: false,
      isActive: false
    },
    {
      id: 5,
      title: 'Önizleme',
      description: 'Programınızı kontrol edin ve kaydedin',
      icon: this.faEye,
      isCompleted: false,
      isActive: false
    }
  ];

  // Forms
  programInfoForm!: FormGroup;
  dayCountForm!: FormGroup;
  daysSetupForm!: FormGroup;

  // Data
  selectedDayCount = 0;
  programDays: any[] = [];
  popularDayNames = POPULAR_DAY_NAMES;
  experienceLevels = EXPERIENCE_LEVELS;
  targetGoals = TARGET_GOALS;

  constructor(
    private fb: FormBuilder,
    private workoutProgramService: WorkoutProgramService,
    private router: Router,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.initializeForms();
  }

  initializeForms(): void {
    // Step 1: Program Info Form
    this.programInfoForm = this.fb.group({
      programName: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]],
      experienceLevel: ['', [Validators.required]],
      targetGoal: ['', [Validators.required]]
    });

    // Step 2: Day Count Form
    this.dayCountForm = this.fb.group({
      dayCount: [0, [Validators.required, Validators.min(1), Validators.max(7)]]
    });

    // Step 3: Days Setup Form
    this.daysSetupForm = this.fb.group({
      days: this.fb.array([])
    });
  }

  get daysArray(): FormArray {
    return this.daysSetupForm.get('days') as FormArray;
  }

  // Navigation Methods
  nextStep(): void {
    if (this.validateCurrentStep()) {
      if (this.currentStep < this.totalSteps) {
        this.steps[this.currentStep - 1].isCompleted = true;
        this.steps[this.currentStep - 1].isActive = false;
        
        this.currentStep++;
        
        this.steps[this.currentStep - 1].isActive = true;
        
        // Handle step-specific logic
        this.handleStepChange();
      }
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.steps[this.currentStep - 1].isActive = false;
      this.currentStep--;
      this.steps[this.currentStep - 1].isActive = true;
      this.steps[this.currentStep - 1].isCompleted = false;
    }
  }

  goToStep(stepNumber: number): void {
    if (stepNumber <= this.currentStep || this.steps[stepNumber - 1].isCompleted) {
      this.steps[this.currentStep - 1].isActive = false;
      this.currentStep = stepNumber;
      this.steps[this.currentStep - 1].isActive = true;
    }
  }

  validateCurrentStep(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.programInfoForm.valid;
      case 2:
        return this.dayCountForm.valid;
      case 3:
        return this.daysSetupForm.valid;
      case 4:
        return this.validateExercises();
      case 5:
        return true;
      default:
        return false;
    }
  }

  validateExercises(): boolean {
    // Her antrenman günü için en az bir egzersiz olmalı
    return this.programDays.every(day => 
      day.isRestDay || (day.exercises && day.exercises.length > 0)
    );
  }

  handleStepChange(): void {
    switch (this.currentStep) {
      case 3:
        this.setupDaysArray();
        break;
      case 4:
        this.initializeExercises();
        break;
    }
  }

  setupDaysArray(): void {
    const dayCount = this.dayCountForm.get('dayCount')?.value;
    this.selectedDayCount = dayCount;
    
    // Clear existing days
    this.daysArray.clear();
    this.programDays = [];

    // Create day forms
    for (let i = 1; i <= dayCount; i++) {
      const dayGroup = this.fb.group({
        dayNumber: [i, [Validators.required]],
        dayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
        isRestDay: [false]
      });

      this.daysArray.push(dayGroup);
      
      this.programDays.push({
        dayNumber: i,
        dayName: '',
        isRestDay: false,
        exercises: []
      });
    }
  }

  initializeExercises(): void {
    // Sync form data with programDays
    this.daysArray.controls.forEach((dayControl, index) => {
      const formValue = dayControl.value;
      this.programDays[index] = {
        ...this.programDays[index],
        dayName: formValue.dayName,
        isRestDay: formValue.isRestDay
      };
    });
  }

  // Day Management
  onPopularDayNameSelect(dayIndex: number, dayName: string): void {
    const dayControl = this.daysArray.at(dayIndex);
    dayControl.get('dayName')?.setValue(dayName);
    
    this.programDays[dayIndex].dayName = dayName;

    if (dayName === 'Dinlenme Günü') {
      dayControl.get('isRestDay')?.setValue(true);
      this.programDays[dayIndex].isRestDay = true;
      this.programDays[dayIndex].exercises = [];
    }
  }

  onRestDayChange(dayIndex: number): void {
    const dayControl = this.daysArray.at(dayIndex);
    const isRestDay = dayControl.get('isRestDay')?.value;
    
    this.programDays[dayIndex].isRestDay = isRestDay;

    if (isRestDay) {
      this.programDays[dayIndex].exercises = [];
      dayControl.get('dayName')?.setValue('Dinlenme Günü');
      this.programDays[dayIndex].dayName = 'Dinlenme Günü';
    } else {
      const currentDayName = dayControl.get('dayName')?.value;
      if (currentDayName === 'Dinlenme Günü') {
        dayControl.get('dayName')?.setValue('');
        this.programDays[dayIndex].dayName = '';
      }
    }
  }

  // Drag and Drop for Days
  dropDay(event: CdkDragDrop<any>): void {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(this.programDays, event.previousIndex, event.currentIndex);
      moveItemInArray(this.daysArray.controls, event.previousIndex, event.currentIndex);
      
      // Update day numbers
      this.programDays.forEach((day, index) => {
        day.dayNumber = index + 1;
        this.daysArray.at(index).get('dayNumber')?.setValue(index + 1);
      });
    }
  }

  // Final Submit
  onSubmit(): void {
    if (this.validateAllSteps()) {
      this.isSubmitting = true;

      const programData: WorkoutProgramTemplateAdd = {
        programName: this.programInfoForm.get('programName')?.value,
        description: this.programInfoForm.get('description')?.value,
        experienceLevel: this.programInfoForm.get('experienceLevel')?.value,
        targetGoal: this.programInfoForm.get('targetGoal')?.value,
        days: this.programDays.map(day => ({
          dayNumber: day.dayNumber,
          dayName: day.dayName,
          isRestDay: day.isRestDay,
          exercises: day.exercises || []
        }))
      };

      this.workoutProgramService.add(programData).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Antrenman programı başarıyla oluşturuldu', 'Başarılı');
            this.router.navigate(['/workout-programs']);
          } else {
            this.toastrService.error(response.message || 'Program oluşturulurken hata oluştu', 'Hata');
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          console.error('Error creating workout program:', error);
          this.toastrService.error('Program oluşturulurken hata oluştu', 'Hata');
          this.isSubmitting = false;
        }
      });
    }
  }

  validateAllSteps(): boolean {
    return this.programInfoForm.valid && 
           this.dayCountForm.valid && 
           this.daysSetupForm.valid && 
           this.validateExercises();
  }

  goBack(): void {
    this.router.navigate(['/workout-programs']);
  }

  // Exercise Management Methods
  addExercise(dayIndex: number): void {
    const newExercise = {
      exerciseType: 'System',
      exerciseID: 0,
      exerciseName: '',
      orderIndex: (this.programDays[dayIndex].exercises?.length || 0) + 1,
      sets: 4,
      reps: '12',
      notes: ''
    };

    if (!this.programDays[dayIndex].exercises) {
      this.programDays[dayIndex].exercises = [];
    }

    this.programDays[dayIndex].exercises.push(newExercise);
  }

  editExercise(dayIndex: number, exerciseIndex: number): void {
    // Bu metod modal açacak - şimdilik basit edit
    const exercise = this.programDays[dayIndex].exercises[exerciseIndex];
    const newName = prompt('Egzersiz adı:', exercise.exerciseName);
    if (newName) {
      exercise.exerciseName = newName;
    }
  }

  removeExercise(dayIndex: number, exerciseIndex: number): void {
    this.programDays[dayIndex].exercises.splice(exerciseIndex, 1);
    // Update order indexes
    this.programDays[dayIndex].exercises.forEach((exercise: any, index: number) => {
      exercise.orderIndex = index + 1;
    });
  }

  // Preview Helper Methods
  getWorkoutDaysCount(): number {
    return this.programDays.filter(day => !day.isRestDay).length;
  }

  getRestDaysCount(): number {
    return this.programDays.filter(day => day.isRestDay).length;
  }

  getTotalExerciseCount(): number {
    return this.programDays.reduce((total, day) => {
      return total + (day.exercises?.length || 0);
    }, 0);
  }

  // Utility Methods
  isFieldInvalid(form: FormGroup, fieldName: string): boolean {
    const field = form.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(form: FormGroup, fieldName: string): string {
    const field = form.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return 'Bu alan zorunludur';
      if (field.errors['minlength']) return `En az ${field.errors['minlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['maxlength']) return `En fazla ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
      if (field.errors['min']) return `En az ${field.errors['min'].min} olmalıdır`;
      if (field.errors['max']) return `En fazla ${field.errors['max'].max} olmalıdır`;
    }
    return '';
  }
}
